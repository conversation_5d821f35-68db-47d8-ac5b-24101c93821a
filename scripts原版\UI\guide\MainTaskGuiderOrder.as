package UI.guide
{
   import UI.task.TaskUI;
   
   public class MainTaskGuiderOrder extends NormalGuideOrder
   {
      
      public function MainTaskGuiderOrder()
      {
         super();
         arr = [this.mainBtn,this.getBtn,this.gotoBtn];
      }
      
      private function mainBtn() : OneGuideData
      {
         return new OneGuideData(Gaming.uiGroup.mainUI.getBtn("task"),"要开放新地图\n请接取主线任务");
      }
      
      private function getBtn() : OneGuideData
      {
         var ui0:TaskUI = Gaming.uiGroup.taskUI;
         ui0.showBox("main");
         return new OneGuideData(ui0.getBtn,"点击领取该主线任务");
      }
      
      private function gotoBtn() : OneGuideData
      {
         var ui0:TaskUI = Gaming.uiGroup.taskUI;
         return new OneGuideData(ui0.mapBtn,"点击直接进入任务地图");
      }
   }
}

