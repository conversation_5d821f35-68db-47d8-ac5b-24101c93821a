package UI.gift.holiday
{
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import com.sounto.oldUtils.StringDate;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class HolidayGiftBoard extends NormalUI
   {
      
      private var giftBtn:NormalBtn = new NormalBtn();
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      private var giftBtnSp:MovieClip;
      
      private var giftTag:Sprite;
      
      private var giftName:String = "dianzan";
      
      public function HolidayGiftBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["giftTag","giftBtnSp"];
         super.setImg(img0);
         this.giftTag.addChild(this.giftBox);
         this.giftBox.arg.init(7,3,14,14);
         this.giftBox.setIconPro("equipGrip");
         this.giftBox.evt.setWantEvent(true,false,false,true,true);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
         addChild(this.giftBtn);
         this.giftBtn.setImg(this.giftBtnSp);
         this.giftBtn.setName("领取礼包");
         this.giftBtn.addEventListener(MouseEvent.CLICK,this.getGiftClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         this.giftBtn.actived = false;
         super.show();
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         this.fleshBtn();
         var gift_dg0:GiftAddDefineGroup = Gaming.defineGroup.gift.getOne(this.giftName);
         this.giftBox.inData_byArr(gift_dg0.arr,"inData_gift");
      }
      
      private function getTimeState(timeStr0:String) : int
      {
         return 1;
      }
      
      private function fleshBtn() : void
      {
         this.giftBtn.setName("领取礼包");
         var now_de0:StringDate = Gaming.api.save.getNowServerDate();
         var state0:int = this.getTimeState(now_de0.getStr());
         if(state0 == 0)
         {
            this.giftBtn.setName("活动未开始");
         }
         else if(state0 == 1)
         {
            this.giftBtn.setName("领取礼包");
         }
         else
         {
            this.giftBtn.setName("活动已结束");
         }
         var haveB0:Boolean = Gaming.PG.save.gift.getGiftGetNum(this.giftName) >= 1;
         if(haveB0)
         {
            this.giftBtn.setName("今日已领取");
         }
         this.giftBtn.actived = state0 == 1 && !haveB0;
      }
      
      private function getGiftClick(e:MouseEvent) : void
      {
         if(Gaming.PG.save.gift.getGiftGetNum(this.giftName) == 0)
         {
            this.getGift();
         }
      }
      
      private function getGift(v:* = null) : void
      {
         var gift_dg0:GiftAddDefineGroup = Gaming.defineGroup.gift.getOne(this.giftName);
         var successB0:Boolean = GiftAddit.addAndAutoBagSpacePan(gift_dg0);
         if(successB0)
         {
            Gaming.PG.save.gift.addGiftGetNum(this.giftName);
            this.fleshBtn();
         }
      }
      
      private function affterGiftClick(v:* = null) : void
      {
         Gaming.uiGroup.alertBox.showSuccess("领取礼包成功！");
      }
   }
}

