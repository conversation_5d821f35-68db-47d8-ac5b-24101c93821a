package UI.helper
{
   import UI.base.AppNormalUI;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import UI.helper.book.HelperBookBoard;
   import UI.helper.book.HelperBookBox;
   import UI.helper.pedia.HelperPediaBoard;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class HelperUI extends AppNormalUI
   {
      
      public var bookBoard:HelperBookBoard;
      
      private var pediaBoard:HelperPediaBoard;
      
      private var bookSp:Sprite;
      
      private var bookBox:HelperBookBox;
      
      private var labelBox:LabelBox;
      
      private var labelArr:Array;
      
      private var boxArr:Array;
      
      private var labelTag:Sprite;
      
      private var closeBtn:SimpleButton;
      
      private var tipBtn:SimpleButton;
      
      public function HelperUI()
      {
         var label0:String = null;
         var box0:NormalUI = null;
         this.bookBoard = new HelperBookBoard();
         this.pediaBoard = new HelperPediaBoard();
         this.bookBox = new HelperBookBox();
         this.labelBox = new LabelBox();
         this.labelArr = ["book","pedia"];
         this.boxArr = [];
         super();
         UICn = "图鉴";
         for each(label0 in this.labelArr)
         {
            box0 = this[label0 + "Board"];
            this.boxArr.push(box0);
            box0.UILabel = label0;
         }
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var box0:NormalUI = null;
         elementNameArr = ["tipBtn","labelTag","closeBtn","bookSp"];
         super.setImg(img0);
         this.labelBox.arg.init(7,1,-6,0);
         addChild(this.labelBox);
         this.labelBox.inData("longLabelBtn",this.labelArr,["图鉴","百科"]);
         this.labelBox.setChoose_byIndex(0);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         this.tipBtn.visible = false;
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         for each(box0 in this.boxArr)
         {
            addChild(box0);
            box0.visible = false;
            box0.setImg(Gaming.swfLoaderManager.getResource("HelperUI",box0.UILabel + "Board"));
         }
         this.bookBox.setImg(this.bookSp);
         addChild(this.bookBox);
         this.bookBox.hide();
         this.bookBoard.contentBox = this.bookBox;
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         var label0:String = this.labelBox.nowLabel;
         this.showBox(label0);
      }
      
      override public function hide() : void
      {
         this.bookBox.hide();
         super.hide();
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         var n:* = undefined;
         var ui0:NormalUI = null;
         if(label0 == "")
         {
            label0 = "info";
         }
         this.labelBox.setChoose(label0);
         for(n in this.boxArr)
         {
            this.boxArr[n].hide();
         }
         ui0 = this[label0 + "Board"];
         if(Boolean(ui0))
         {
            ui0.show();
         }
      }
      
      private function tipOver(e:MouseEvent) : void
      {
         var str0:String = "";
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
   }
}

