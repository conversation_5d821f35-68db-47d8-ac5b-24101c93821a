package UI.helper.pedia
{
   import UI.NormalUICtrl;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.label.MoreLabelBox;
   import UI.base.scroll.NormalScrollBar;
   import UI.helper.HeplperLinkText;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.body.attack.ElementShell;
   import dataAll.body.define.BodyFather;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.drop.DropSave;
   import dataAll.ui.label.LabelAddData;
   import flash.display.Sprite;
   import flash.text.TextField;
   import gameAll.drop.bodyDrop.ArmsBodyDrop;
   import gameAll.drop.bodyDrop.EquipBodyDrop;
   import gameAll.drop.bodyDrop.GemBodyDrop;
   import gameAll.drop.bodyDrop.PartsBodyDrop;
   
   public class HelperPediaBoard extends NormalUI
   {
      
      private var leftTag:Sprite;
      
      private var nowType:String = "";
      
      private var nowChildType:String = "";
      
      private var leftBox:MoreLabelBox = new MoreLabelBox();
      
      private var contextSp:Sprite = new Sprite();
      
      private var context:Sprite = null;
      
      private var scrollBar:NormalScrollBar;
      
      private var contextScrollBar:NormalScrollBar;
      
      private var help_save:HeplperSaveSp = new HeplperSaveSp();
      
      private var shellCountB:Boolean = false;
      
      public function HelperPediaBoard()
      {
         super();
      }
      
      protected static function get dropSave() : DropSave
      {
         return Gaming.PG.save.drop;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["leftTag"];
         super.setImg(img0);
         img0.addChild(this.leftBox);
         NormalUICtrl.setTag(this.leftBox,this.leftTag);
         img0.addChild(this.contextSp);
         NormalUICtrl.setTag(this.contextSp,img0["maskTargetSp2"]);
         this.addLeftLabel();
         this.scrollBar = new NormalScrollBar(this.leftBox,img0["maskTargetSp"],img0["scrollBarSp"],img0["scrollLineSp"],1,false,true,true);
         this.scrollBar.speed = 30;
         this.scrollBar.refresh();
         this.contextScrollBar = new NormalScrollBar(this.contextSp,img0["maskTargetSp2"],img0["scrollBarSp2"],img0["scrollLineSp2"],1,false,true,true);
         this.contextScrollBar.speed = 30;
         this.contextScrollBar.elastic = true;
         this.contextScrollBar.tween = 5;
         this.contextScrollBar.refresh();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function addLeftLabel() : void
      {
         var da0:LabelAddData = this.getLabelAddData();
         this.leftBox.inDataByLabelAddData(da0);
         this.leftBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
      }
      
      private function getLabelAddData() : LabelAddData
      {
         var n:* = undefined;
         var name1:String = null;
         var cn1:String = null;
         var fArr0:Array = null;
         var fCnArr0:Array = null;
         var da1:LabelAddData = null;
         var i:* = undefined;
         var name2:String = null;
         var cn2:String = null;
         var da2:LabelAddData = null;
         var gatherNameArr0:Array = ["help","drop","ele"];
         var gatherCnArr0:Array = ["问题大全","物品与掉落","元素伤害"];
         var fatherNameArr0:Array = [];
         var fatherCnArr0:Array = [];
         fatherNameArr0.push(["save","piano"]);
         fatherCnArr0.push(["存档和充值","乐谱编辑"]);
         fatherNameArr0.push(["arms","equip","device","things","parts","rareParts"]);
         fatherCnArr0.push(["武器","装备","装置副手","物品","特殊零件","稀有零件"]);
         fatherNameArr0.push(["elementShell","shellCount"]);
         fatherCnArr0.push(["元素与外壳","单位外壳统计"]);
         var da0:LabelAddData = new LabelAddData();
         da0.inDataOne("helper","助手","HelperUI/bigLabel",0);
         for(n in gatherNameArr0)
         {
            name1 = gatherNameArr0[n];
            cn1 = gatherCnArr0[n];
            fArr0 = fatherNameArr0[n];
            fCnArr0 = fatherCnArr0[n];
            da1 = new LabelAddData();
            da1.inDataOne(name1,cn1,"HelperUI/midLabel",0);
            for(i in fArr0)
            {
               name2 = fArr0[i];
               cn2 = fCnArr0[i];
               da2 = new LabelAddData();
               da2.inDataOne(name2,cn2,"HelperUI/midLabel",i);
               da1.addChildData(da2);
            }
            da0.addChildData(da1);
         }
         return da0;
      }
      
      override public function show() : void
      {
         super.show();
         this.showLabel(this.nowType,this.nowChildType);
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         var strArr0:Array = e.fatherUrl.split("/");
         var type0:String = strArr0[strArr0.length - 2];
         var childType0:String = e.label;
         if(e.father != e.target)
         {
            this.showLabel(type0,childType0);
         }
         this.scrollBar.refresh();
      }
      
      private function showLabel(type0:String, childType0:String) : void
      {
         var linkSp0:HeplperLinkText = null;
         if(this.nowType == "" || this.nowChildType == "")
         {
            type0 = "drop";
            childType0 = "arms";
            this.leftBox.showChildVisibleByName(type0,childType0);
         }
         this.nowType = type0;
         this.nowChildType = childType0;
         this.clearContextSp();
         var sp0:Sprite = this.getContext(type0 + "_" + childType0);
         if(sp0 is Sprite)
         {
            linkSp0 = sp0 as HeplperLinkText;
            if(Boolean(linkSp0))
            {
               linkSp0.tryNormalImg();
            }
            this.context = sp0;
            this.contextSp.addChild(sp0);
            sp0.x = 6;
            sp0.y = 12;
            if(!linkSp0)
            {
               this.contextSp.graphics.clear();
               this.contextSp.graphics.beginFill(0,0);
               this.contextSp.graphics.drawRect(0,0,sp0.x * 2 + sp0.width,sp0.y * 2 + sp0.height);
            }
         }
         this.contextScrollBar.refresh();
         this.contextScrollBar.setPer(0);
      }
      
      private function getContext(label0:String) : Sprite
      {
         var sp0:Sprite = Gaming.swfLoaderManager.getResource("HelperUI",label0);
         if(!sp0)
         {
            sp0 = this[label0];
         }
         if(label0 == "drop_arms")
         {
            this.drop_arms(sp0);
         }
         if(label0 == "drop_equip")
         {
            this.drop_equip(sp0);
         }
         if(label0 == "ele_elementShell")
         {
            this.ele_elementShell(sp0);
         }
         if(label0 == "drop_parts")
         {
            this.drop_parts(sp0);
         }
         if(label0 == "ele_shellCount")
         {
            this.ele_shellCount(sp0);
         }
         return sp0;
      }
      
      private function drop_arms(sp0:Sprite) : void
      {
         (sp0["txt"] as TextField).text = ArmsBodyDrop.getArmsBlackDropStr86();
         (sp0["txt2"] as TextField).text = ArmsBodyDrop.getArmsBlackDropStr();
      }
      
      private function drop_equip(sp0:Sprite) : void
      {
         (sp0["txt"] as TextField).text = EquipBodyDrop.getEquipBlackDropStr();
         (sp0["txt2"] as TextField).text = EquipBodyDrop.getEquipBlackDropStr86();
      }
      
      private function ele_elementShell(sp0:Sprite) : void
      {
         (sp0["txt"] as TextField).text = GemBodyDrop.getGemDropStr();
      }
      
      private function drop_parts(sp0:Sprite) : void
      {
         (sp0["txt"] as TextField).text = PartsBodyDrop.getPartsDropStr();
      }
      
      private function ele_shellCount(sp0:Sprite) : void
      {
         var shell0:String = null;
         var txtName0:String = null;
         var txt0:TextField = null;
         var s0:String = null;
         var shellArr0:Array = null;
         var d0:NormalBodyDefine = null;
         var title0:String = null;
         var color0:String = null;
         this.shellCountB = true;
         var shellObj0:Object = Gaming.defineGroup.body.getUIShellObj();
         for each(shell0 in ElementShell.arr)
         {
            txtName0 = shell0 + "Txt";
            if(sp0.hasOwnProperty(txtName0))
            {
               txt0 = sp0[txtName0] as TextField;
               if(Boolean(txt0))
               {
                  FontDeal.dealLine(txt0);
                  s0 = "";
                  shellArr0 = shellObj0[shell0];
                  if(Boolean(shellArr0))
                  {
                     for each(d0 in shellArr0)
                     {
                        title0 = d0.getRoleCn();
                        color0 = BodyFather.getEleColor(d0.father);
                        if(color0 != "")
                        {
                           if(d0.father == BodyFather.enemy)
                           {
                              if(d0.isSpecialBoss() == false)
                              {
                                 color0 = "";
                              }
                           }
                        }
                        if(color0 != "")
                        {
                           title0 = ComMethod.color(title0,color0);
                        }
                        s0 += title0 + "\n";
                     }
                  }
                  txt0.htmlText = FontDeal.getDealLeadingStr(txt0,s0);
               }
            }
         }
      }
      
      private function clearContextSp() : void
      {
         ComMethod.clearAllChildren(this.contextSp);
      }
   }
}

