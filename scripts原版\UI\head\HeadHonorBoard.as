package UI.head
{
   import UI.base.NormalUI;
   import UI.base.scroll.NormalScrollBar;
   import dataAll._app.head.define.HeadHonorDefine;
   import flash.display.Sprite;
   
   public class HeadHonorBoard extends NormalUI
   {
      
      private var barArr:Array = [];
      
      private var barTag:Sprite;
      
      private var showB:Boolean = false;
      
      private var con:Sprite = new Sprite();
      
      public var infoBox:HeadInfoBox = new HeadInfoBox();
      
      private var scrollBar:NormalScrollBar;
      
      public function HeadHonorBoard()
      {
         super();
         addChild(this.con);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["barTag"];
         super.setImg(img0);
         this.scrollBar = new NormalScrollBar(this.barTag,img0["maskTargetSp"],img0["scrollBarSp"],img0["scrollLineSp"],1,false,true,true);
         this.scrollBar.speed = 30;
         this.scrollBar.refresh();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.infoBox.visible = false;
         if(!this.showB)
         {
            this.addBar();
            this.showB = true;
         }
         this.fleshData();
      }
      
      private function addBar() : void
      {
         var d0:HeadHonorDefine = null;
         var bar0:HeadHonorGrip = null;
         var arr0:Array = Gaming.defineGroup.head.honorArr;
         var n0:int = 0;
         for each(d0 in arr0)
         {
            bar0 = new HeadHonorGrip();
            bar0.setImg(Gaming.swfLoaderManager.getResource("HeadUI","honorGrip"));
            this.barTag.addChild(bar0);
            bar0.y = n0 * 80;
            bar0.inData_byHonorDefine(d0);
            this.barArr.push(bar0);
            n0++;
         }
         this.scrollBar.refresh();
      }
      
      private function fleshData() : void
      {
         var grip0:HeadHonorGrip = null;
         var nowHonor0:Number = Gaming.PG.da.head.getNowHonor();
         for each(grip0 in this.barArr)
         {
            grip0.fleshByNowHonor(nowHonor0);
         }
      }
   }
}

