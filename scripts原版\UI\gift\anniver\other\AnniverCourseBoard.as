package UI.gift.anniver.other
{
   import UI.base.NormalUI;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.worldMap.save.WorldMapCount;
   import dataAll._player.PlayerData;
   import dataAll._player.count.PlayerCountSave;
   import dataAll._player.more.MoreData;
   import dataAll.arms.save.ArmsSave;
   import dataAll.drop.DropSave;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSave;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.save.ItemsSave;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class AnniverCourseBoard extends NormalUI
   {
      
      private var txt1:TextField;
      
      private var txt2:TextField;
      
      private var txt3:TextField;
      
      private var nameTxt:TextField;
      
      public function AnniverCourseBoard()
      {
         super();
      }
      
      private static function getEarliestStr(daArr0:Array, partType0:String = "") : String
      {
         var da0:IO_ItemsData = null;
         var s0:ItemsSave = null;
         var time0:String = null;
         var st0:StringDate = null;
         var t0:Number = NaN;
         var mostT0:Number = 9999999999999;
         var timeCn0:String = "";
         var cn0:String = "";
         for each(da0 in daArr0)
         {
            s0 = da0.getSave();
            if(partType0 == "" || s0.getChildType() == partType0)
            {
               time0 = s0.getGetTime();
               if(StringDate.isZero(time0) == false)
               {
                  st0 = new StringDate(time0);
                  t0 = Number(st0.getDateClass().getTime());
                  if(t0 < mostT0)
                  {
                     mostT0 = t0;
                     timeCn0 = st0.getDateStr();
                     cn0 = s0.getBaseCn();
                  }
               }
            }
         }
         if(timeCn0 != "")
         {
            return cn0 + " " + timeCn0;
         }
         return "无";
      }
      
      private static function getStrenNum(daArr0:Array) : Number
      {
         var da0:IO_ItemsData = null;
         var s0:ItemsSave = null;
         var armsS0:ArmsSave = null;
         var equipS0:EquipSave = null;
         var num0:Number = 0;
         for each(da0 in daArr0)
         {
            s0 = da0.getSave();
            armsS0 = s0 as ArmsSave;
            equipS0 = s0 as EquipSave;
            if(Boolean(armsS0))
            {
               num0 += armsS0.strengthenNum;
            }
            else if(Boolean(equipS0))
            {
               num0 += equipS0.strengthenNum;
            }
         }
         return num0;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = elementNameArr.concat(["txt1","txt2","txt3","nameTxt"]);
         super.setImg(img0);
         FontDeal.dealLine(this.txt1);
         FontDeal.dealLine(this.txt2);
         FontDeal.dealLine(this.txt3);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      private function fleshData() : void
      {
         var mda0:MoreData = null;
         var s2:String = null;
         var armsArr0:Array = null;
         var equipArr0:Array = null;
         var s3:String = null;
         var pd0:PlayerData = Gaming.PG.da;
         var pc0:PlayerCountSave = Gaming.PG.save.getCount();
         var mc0:WorldMapCount = Gaming.PG.save.worldMap.getMapCount();
         var marr0:Array = Gaming.PG.da.getMoreDataArr();
         var drop0:DropSave = Gaming.PG.save.drop;
         var s1:String = "";
         s1 += this.c("建档时间：") + Gaming.PG.loginData.save.create_time;
         s1 += this.c("\n存档次数：") + Gaming.PG.loginData.save.update_times;
         s1 += this.c("\n击毙敌人总数：") + pd0.moreWay.getAll_countNum("killNum");
         s1 += this.c("\n击毙首领总数：") + pd0.moreWay.getAll_countNum("killBossNum");
         s1 += this.c("\n使用载具击毙敌人：") + pc0.vehicleKillNum;
         s1 += this.c("\n使用副手击毙敌人：") + pc0.weaponKillNum;
         for each(mda0 in marr0)
         {
            s1 += this.c("\n" + mda0.def.getRoleCn() + "击毙敌人：") + mda0.DATA.getNormalSave().count.killNum + "个";
         }
         this.txt3.htmlText = FontDeal.getDealLeadingStr(this.txt3,s1);
         s2 = "";
         armsArr0 = pd0.getArmsDataArr(true,true,true,true);
         equipArr0 = pd0.getEquipDataArr(true,true,true,false,true);
         s2 += this.c("存档中最早的物品");
         s2 += this.c("\n武器：") + getEarliestStr(armsArr0);
         s2 += this.c("\n战衣：") + getEarliestStr(equipArr0,EquipType.COAT);
         s2 += this.c("\n战裤：") + getEarliestStr(equipArr0,EquipType.PANTS);
         s2 += this.c("\n腰带：") + getEarliestStr(equipArr0,EquipType.BELT);
         s2 += this.c("\n头盔：") + getEarliestStr(equipArr0,EquipType.HEAD);
         s2 += this.c("\n时装：") + getEarliestStr(equipArr0,EquipType.FASHION);
         s2 += this.c("\n载具：") + getEarliestStr(equipArr0,EquipType.VEHICLE);
         s2 += this.c("\n副手：") + getEarliestStr(equipArr0,EquipType.WEAPON);
         s2 += this.c("\n装置：") + getEarliestStr(equipArr0,EquipType.DEVICE);
         s2 += this.c("\n饰品：") + getEarliestStr(equipArr0,EquipType.JEWELRY);
         s2 += this.c("\n护盾：") + getEarliestStr(equipArr0,EquipType.SHIELD);
         this.txt2.htmlText = FontDeal.getDealLeadingStr(this.txt2,s2);
         s3 = "";
         s3 += this.c("所有成员战力之和：") + NumberMethod.toFixedWan(pd0.moreWay.getAllDps());
         s3 += this.c("\n射出子弹总数：") + mc0.count.bulletNum;
         s3 += this.c("\n殒命总次数：") + pc0.dieNum;
         s3 += this.c("\n重生总次数：") + mc0.count.rebirthNum;
         s3 += this.c("\n通关总次数：") + mc0.winNum;
         s3 += this.c("\n通关总时长：") + Math.ceil(mc0.count.time / 60 / 60) + "小时";
         s3 += this.c("\n获得星级累计：") + mc0.count.star;
         s3 += this.c("\n挑战最多的地图：") + mc0.mostMapCn + " " + mc0.mostNum + "次";
         s3 += this.c("\n熔炼总次数：") + pd0.city.save.allNum;
         s3 += this.c("\n食物掉落总数：") + pd0.food.save.dropAll;
         s3 += this.c("\n食物进食累计：") + pd0.food.save.eatAll + "次";
         s3 += this.c("\n给队友送礼累计：") + pd0.moreWay.getLoveGiftAll() + "次";
         s1 += this.c("\n秘境挑战总累计：") + Gaming.PG.da.wilder.getWinAll() + "次";
         s1 += this.c("\n挑战最多的秘境：") + Gaming.PG.da.wilder.getMostWiderStr();
         this.txt1.htmlText = FontDeal.getDealLeadingStr(this.txt1,s3);
         this.nameTxt.text = pd0.heroData.getCnName();
      }
      
      private function c(str0:String) : String
      {
         return ComMethod.color(str0,"#C0C2FC");
      }
   }
}

