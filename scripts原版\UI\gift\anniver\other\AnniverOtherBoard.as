package UI.gift.anniver.other
{
   import UI.base.AutoNormalUI;
   import flash.display.Sprite;
   
   public class AnniverOtherBoard extends AutoNormalUI
   {
      
      public function AnniverOtherBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      private function fleshData() : void
      {
      }
   }
}

