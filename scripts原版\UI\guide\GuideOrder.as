package UI.guide
{
   import dataAll._app.task.TaskData;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll.ui.guide.GuideSave;
   import flash.display.DisplayObject;
   
   public class GuideOrder
   {
      
      private static var first:FirstGuideOrder = new FirstGuideOrder();
      
      private static var arms:ArmsGuideOrder = new ArmsGuideOrder();
      
      private static var equip:EquipGuideOrder = new EquipGuideOrder();
      
      private static var more:MoreGuideOrder = new MoreGuideOrder();
      
      private static var skill:SkillGuideOrder = new SkillGuideOrder();
      
      private static var task:TaskGuideOrder = new TaskGuideOrder();
      
      private static var mainTask:MainTaskGuiderOrder = new MainTaskGuiderOrder();
      
      private static var endless:EndlessGuideOrder = new EndlessGuideOrder();
      
      private static var name:String = "";
      
      private static var index:int = -1;
      
      private static var arr:Array = [];
      
      private static var da:OneGuideData = null;
      
      public function GuideOrder()
      {
         super();
      }
      
      public static function guidePan() : void
      {
         var openB0:Boolean = false;
         if(Gaming.LG.mode != "normal" || Gaming.LG.mapMode == MapMode.ENDLESS)
         {
            return;
         }
         if(Boolean(Gaming.PG.da.worldMap.saveGroup.getSave("XiaSha")))
         {
            open("arms");
         }
         if(Gaming.PG.da.equipBag.dataArr.length > 0)
         {
            open("equip");
         }
         if(Gaming.uiGroup.mainUI.getBtn("skill").secoundEnabled)
         {
            open("skill");
         }
         if(Gaming.uiGroup.mainUI.getBtn("task").secoundEnabled)
         {
            open("task");
         }
         var taskDa0:TaskData = Gaming.PG.da.task.getTaskDataByName("BaiLu_ZangShi");
         if(Boolean(taskDa0))
         {
            if(taskDa0.state == "no")
            {
               open("mainTask");
            }
         }
      }
      
      public static function haveDa() : Boolean
      {
         return da;
      }
      
      public static function open(name0:String) : Boolean
      {
         var or0:NormalGuideOrder = null;
         overGuide();
         var s0:GuideSave = Gaming.PG.save.guide;
         if(!s0[name0])
         {
            or0 = GuideOrder[name0];
            if(or0 is NormalGuideOrder)
            {
               name = name0;
               Gaming.uiGroup.moreBox.chooseMainData();
               openArr(or0.arr);
               Gaming.PG.save.guide.overOne(name);
               return true;
            }
         }
         return false;
      }
      
      private static function openArr(arr0:Array) : void
      {
         index = 0;
         arr = arr0;
         dealBtn();
      }
      
      private static function doEvent(e:Object) : void
      {
         da.btn.removeEventListener(da.eventName,doEvent);
         if(da.delay > 0)
         {
            da.state = "delaying";
         }
         else
         {
            da.state = "doed";
         }
      }
      
      private static function dealBtn() : void
      {
         var fun0:Function = null;
         if(index >= arr.length)
         {
            overGuide();
         }
         else
         {
            fun0 = arr[index];
            da = fun0();
            if(da is OneGuideData)
            {
               if(da.btn is DisplayObject)
               {
                  da.btn.addEventListener(da.eventName,doEvent);
                  Gaming.uiGroup.guideBox.showText(da.btn,da.text,da.secondBtn);
               }
               else
               {
                  overGuide();
               }
            }
            else
            {
               overGuide();
            }
         }
      }
      
      public static function overGuide() : void
      {
         if(da is OneGuideData)
         {
            if(da.state == "wait")
            {
               if(Boolean(da.btn))
               {
                  da.btn.removeEventListener(da.eventName,doEvent);
               }
            }
         }
         Gaming.uiGroup.guideBox.hide();
         if(Boolean(Gaming.PG.save))
         {
            Gaming.PG.save.guide.overOne(name);
         }
         da = null;
         index = -1;
         arr = [];
         name = "";
      }
      
      public static function FTimer() : void
      {
         if(index >= 0)
         {
            if(da is OneGuideData)
            {
               if(da.state == "doed")
               {
                  ++index;
                  dealBtn();
               }
               else if(da.state == "delaying")
               {
                  da.t += 1 / 30;
                  if(da.t >= da.delay)
                  {
                     da.state = "doed";
                  }
               }
            }
            else
            {
               overGuide();
            }
         }
      }
   }
}

