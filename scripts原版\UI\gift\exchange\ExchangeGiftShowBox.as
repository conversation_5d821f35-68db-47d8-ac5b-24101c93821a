package UI.gift.exchange
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import com.greensock.TweenLite;
   import com.sounto.oldUtils.StringDate;
   import dataAll.gift.define.ExchangeGiftAddDefineGroup;
   import fl.motion.easing.Back;
   import flash.display.DisplayObject;
   import flash.display.FrameLabel;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class ExchangeGiftShowBox extends NormalUI
   {
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      private var pointerSp:Sprite;
      
      private var giftTag:Sprite;
      
      private var gripArr:Array = [];
      
      private var infoMc:MovieClip;
      
      private var nowGrip:ExchangeGiftTitleGrip;
      
      private var btnSp:MovieClip;
      
      public var btn:NormalBtn = new NormalBtn();
      
      public var tileGripResoureName:String = "exchangeTitleGrip";
      
      public var giftFather:String = "activity";
      
      public function ExchangeGiftShowBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["pointerSp","giftTag","btnSp","infoMc"];
         super.setImg(img0);
         if(Boolean(this.infoMc))
         {
            this.infoMc.stop();
            this.infoMc.mouseChildren = false;
            this.infoMc.mouseEnabled = false;
         }
         this.giftBox.setIconPro("equipGrip",50,50);
         this.giftBox.arg.init(6,3,8,8);
         this.giftBox.evt.setWantEvent(true,false,false,true,true);
         addChild(this.giftBox);
         NormalUICtrl.setTag(this.giftBox,this.giftTag);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("兑换礼包");
      }
      
      public function getGiftBox() : ItemsGripBox
      {
         return this.giftBox;
      }
      
      public function getNowGiftDefine() : ExchangeGiftAddDefineGroup
      {
         return this.nowGrip.giftDefine;
      }
      
      private function addBtnBox(now0:StringDate) : void
      {
         var d0:ExchangeGiftAddDefineGroup = null;
         var grip0:ExchangeGiftTitleGrip = null;
         this.clearAllBtn();
         var arr0:Array = Gaming.defineGroup.gift.getArrByFather(this.giftFather,now0);
         var before_y:int = 0;
         for each(d0 in arr0)
         {
            if(!d0.hideB)
            {
               grip0 = new ExchangeGiftTitleGrip();
               grip0.setToNormalImg(this.tileGripResoureName);
               grip0.inData(d0);
               grip0.y = before_y;
               before_y = grip0.y + grip0.height + 10;
               grip0.addEventListener(MouseEvent.MOUSE_OVER,this.gripOver);
               addChild(grip0);
               this.gripArr.push(grip0);
            }
         }
         addChild(this.btn);
      }
      
      private function clearAllBtn() : void
      {
         var grip0:ExchangeGiftTitleGrip = null;
         for each(grip0 in this.gripArr)
         {
            grip0.clear();
            grip0.removeEventListener(MouseEvent.MOUSE_OVER,this.gripOver);
            removeChild(grip0);
         }
         this.gripArr.length = 0;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function fleshByTime(now0:StringDate) : void
      {
         var grip0:ExchangeGiftTitleGrip = null;
         this.addBtnBox(now0);
         for each(grip0 in this.gripArr)
         {
            grip0.fleshTime(now0);
         }
         this.showOneGift(this.nowGrip);
      }
      
      public function fleshData() : void
      {
         this.showOneGift(null);
      }
      
      private function showInfo(name0:String) : void
      {
         var cfarr0:Array = null;
         var f0:FrameLabel = null;
         if(Boolean(this.infoMc))
         {
            cfarr0 = this.infoMc.currentLabels;
            for each(f0 in cfarr0)
            {
               if(f0.name == name0)
               {
                  this.infoMc.gotoAndStop(name0);
                  return;
               }
            }
            this.infoMc.gotoAndStop(1);
         }
      }
      
      private function gripOver(e:MouseEvent) : void
      {
         if(e.target is ExchangeGiftTitleGrip)
         {
            this.showOneGift(e.target as ExchangeGiftTitleGrip);
         }
         else if(e.target is DisplayObject)
         {
            if(e.target.parent is ExchangeGiftTitleGrip)
            {
               this.showOneGift(e.target.parent as ExchangeGiftTitleGrip);
            }
         }
      }
      
      private function showOneGift(grip0:ExchangeGiftTitleGrip) : void
      {
         var d0:ExchangeGiftAddDefineGroup = null;
         if(grip0 == null)
         {
            grip0 = this.gripArr[0];
         }
         if(this.nowGrip != grip0)
         {
            this.showLinkVisible(grip0);
            this.nowGrip = grip0;
            d0 = grip0.giftDefine;
            this.showInfo(d0.name);
            this.giftBox.inData_byArr(d0.arr,"inData_gift");
            this.giftBox.alpha = 0;
            TweenLite.to(this.pointerSp,0.4,{
               "y":grip0.y + grip0.height / 2,
               "ease":Back.easeOut
            });
            TweenLite.to(this.giftBox,0.4,{"alpha":1});
         }
         if(Boolean(this.nowGrip))
         {
            d0 = this.nowGrip.giftDefine;
            if(d0.giftSave != "")
            {
               this.btn.actived = !Gaming.PG.save.gift[d0.giftSave];
            }
            else
            {
               this.btn.actived = true;
            }
         }
         else
         {
            this.btn.actived = false;
         }
      }
      
      private function showLinkVisible(grip0:ExchangeGiftTitleGrip) : void
      {
         var g0:ExchangeGiftTitleGrip = null;
         for each(g0 in this.gripArr)
         {
            g0.setLinkVisible(g0 == grip0);
         }
      }
   }
}

