package UI.gift.anniver
{
   import UI.base.AppNormalUI;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import UI.gift.anniver.other.AnniverEggBoard;
   import UI.gift.anniver.other.AnniverOtherBoard;
   import UI.gift.anniver.other.AnniverPayBoard;
   import UI.gift.anniver.other.AnniverRoleBoard;
   import UI.gift.anniver.other.AnniverTaskBoard;
   import com.sounto.oldUtils.StringDate;
   import dataAll._data.ConstantDefine;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class AnniverUI extends AppNormalUI
   {
      
      public var labelBox:LabelBox;
      
      private var tipBtn:SimpleButton;
      
      private var signBoard:HolidaySignGiftBoard;
      
      private var eggBoard:AnniverEggBoard;
      
      private var taskBoard:AnniverTaskBoard;
      
      private var roleBoard:AnniverRoleBoard;
      
      private var payBoard:AnniverPayBoard;
      
      private var otherBoard:AnniverOtherBoard;
      
      private var labelArr:Array;
      
      private var labelCnArr:Array;
      
      private var noTimeLimitArr:Array;
      
      private var boxArr:Array;
      
      private var coverSp:Sprite;
      
      private var coverTxt:TextField;
      
      private var labelTag:Sprite;
      
      private var closeBtn:SimpleButton;
      
      private var timeTxt:TextField;
      
      private var timeSp:Sprite;
      
      public function AnniverUI()
      {
         var label0:String = null;
         var box0:NormalUI = null;
         this.labelBox = new LabelBox();
         this.signBoard = new HolidaySignGiftBoard();
         this.eggBoard = new AnniverEggBoard();
         this.taskBoard = new AnniverTaskBoard();
         this.roleBoard = new AnniverRoleBoard();
         this.payBoard = new AnniverPayBoard();
         this.otherBoard = new AnniverOtherBoard();
         this.labelArr = ["sign","egg","task","role","pay","other"];
         this.labelCnArr = ["签到礼包","拆红包","新武器","齐上阵","返利20%","其他更新"];
         this.noTimeLimitArr = ["task","role","pay","other"];
         this.boxArr = [];
         super();
         UICn = "周年庆";
         for each(label0 in this.labelArr)
         {
            box0 = this[label0 + "Board"];
            this.boxArr.push(box0);
            box0.UILabel = label0;
            if(box0.hasOwnProperty("setCoverText"))
            {
               box0["setCoverText"] = this.setCoverText;
            }
         }
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var label0:String = null;
         var normalUI0:NormalUI = null;
         elementNameArr = ["labelTag","closeBtn","coverSp","timeSp","timeTxt","tipBtn"];
         super.setImg(img0);
         var cnNameArr0:Array = this.labelCnArr;
         this.labelBox.arg.init(10,1,-9,0);
         this.labelBox.inData("longLabelBtn",this.labelArr,cnNameArr0);
         this.labelBox.setChoose(this.labelArr[0]);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         addChild(this.labelBox);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         var boardArr0:Array = this.labelArr;
         for each(label0 in boardArr0)
         {
            label0 += "Board";
            normalUI0 = this[label0];
            addChild(normalUI0);
            normalUI0.visible = false;
            normalUI0.setImg(Gaming.swfLoaderManager.getResource("AnniverUI",label0));
         }
         addChild(this.coverSp);
         this.coverTxt = this.coverSp["txt"];
         this.setCoverText("");
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.connectUI.show();
         this.afterGetTime(Gaming.PG.da.time.getReadTime());
      }
      
      private function afterGetTime(timeStr0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         this.showBox(this.labelArr[0]);
      }
      
      private function noGetTime(v0:* = null) : void
      {
         Gaming.uiGroup.connectUI.hide();
         hide();
         Gaming.uiGroup.alertBox.showError("服务器时间获取错误！");
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         var n:* = undefined;
         var ui0:NormalUI = null;
         var timeStr0:String = null;
         var day0:int = 0;
         if(label0 == "")
         {
            label0 = this.labelArr[0];
         }
         this.labelBox.setChoose(label0);
         for(n in this.boxArr)
         {
            this.boxArr[n].hide();
         }
         ui0 = this[label0 + "Board"];
         if(Boolean(ui0))
         {
            ui0.show();
         }
         this.setCoverText("");
         this.timeTxt.visible = false;
         this.timeSp.visible = false;
         if(this.noTimeLimitArr.indexOf(this.labelBox.nowLabel) >= 0)
         {
            this.timeTxt.visible = false;
            this.timeSp.visible = false;
         }
         else
         {
            this.timeTxt.visible = true;
            this.timeSp.visible = true;
            timeStr0 = Gaming.PG.da.time.getReadTime();
            if(StringDate.compareDateByStr(ConstantDefine.anniver.signStart,timeStr0) < 0)
            {
               this.timeTxt.htmlText = "活动还未开始";
               this.setCoverText(ConstantDefine.anniver.getSignTimeStr());
            }
            else
            {
               day0 = this.getEndGap();
               if(day0 >= 0)
               {
                  this.timeTxt.htmlText = "距离活动结束还剩余" + (day0 + 1) + "天";
               }
               else
               {
                  this.timeTxt.htmlText = "活动已结束！";
                  if(label0 == "sign")
                  {
                     this.setCoverText("");
                  }
                  else
                  {
                     this.setCoverText("活动已结束！");
                  }
               }
            }
         }
      }
      
      public function getEndGap() : int
      {
         var timeStr0:String = Gaming.PG.da.time.getReadTime();
         return StringDate.compareDateByStr(timeStr0,ConstantDefine.anniver.signEnd);
      }
      
      public function setCoverText(str0:String) : void
      {
         this.coverSp.visible = str0 != "";
         this.coverTxt.htmlText = str0;
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
      
      private function tipOver(e:MouseEvent) : void
      {
         var str0:String = "";
         if(this.signBoard.visible)
         {
            str0 += "1、每天登陆即可领取每日礼包。";
            str0 += "\n2、签到累计指定天数后还可以获得额外奖励。";
         }
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      public function outLoginEvent() : void
      {
      }
      
      public function FTimerSecond() : void
      {
         if(visible)
         {
         }
      }
   }
}

