package UI.gift.level
{
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.gift.level.LevelGiftData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class GiftLevelBar extends NormalUI
   {
      
      private var levelTxt:TextField;
      
      private var mapTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var coverSp:Sprite;
      
      private var giftTag:Sprite;
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      private var btnSp:MovieClip;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var itemsData:LevelGiftData;
      
      public function GiftLevelBar()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["levelTxt","mapTxt","giftTag","btnSp","coverSp","infoTxt"];
         super.setImg(img0);
         this.coverSp.visible = false;
         this.giftTag.addChild(this.giftBox);
         this.giftBox.arg.init(8,1,1,1);
         this.giftBox.setIconPro("equipGrip",50,50);
         this.giftBox.evt.setWantEvent(true,false,false,true,true);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
         addChild(this.coverSp);
         this.coverSp.mouseChildren = false;
         this.coverSp.mouseEnabled = false;
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("领取");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         FontDeal.dealLine(this.infoTxt);
         addChild(this.infoTxt);
         this.infoTxt.mouseEnabled = false;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function inData(da0:LevelGiftData) : void
      {
         this.itemsData = da0;
         if(this.giftBox.gripArr.length == 0)
         {
            this.giftBox.inData_byArr(da0.define.arr,"inData_gift");
         }
         this.levelTxt.htmlText = ComMethod.color(da0.define.mustLevel + "",da0.openB ? "#FFFF00" : "#999999");
         this.mapTxt.htmlText = da0.taskStr;
         this.infoTxt.htmlText = FontDeal.getDealLeadingStr(this.infoTxt,da0.define.info);
         if(da0.getGiftB)
         {
            this.btn.actived = false;
            this.btn.setName("已领取");
            this.coverSp.visible = true;
         }
         else
         {
            this.btn.actived = da0.openB;
            this.btn.setName(da0.openB ? "领取奖励" : "未开启");
            this.coverSp.visible = !da0.openB;
         }
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var e0:ClickEvent = new ClickEvent();
         e0.childData = this.itemsData;
         e0.child = this;
         dispatchEvent(e0);
      }
   }
}

