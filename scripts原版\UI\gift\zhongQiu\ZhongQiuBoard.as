package UI.gift.zhongQiu
{
   import UI.UIOrder;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.goods.define.PriceType;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.zhongQiu.DicePackDefine;
   import dataAll.gift.zhongQiu.ZhongQiuSave;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.text.TextField;
   
   public class ZhongQiuBoard extends AutoNormalUI
   {
      
      private var infoSp:Sprite;
      
      private var diceSp:Sprite;
      
      private var giftTxt:TextField;
      
      private var numTxt:TextField;
      
      private var timeTxt:TextField;
      
      private var startBtn:NormalBtn;
      
      private var infoBtn:NormalBtn;
      
      private var shopBtn:NormalBtn;
      
      private var tipBtn:NormalBtn;
      
      private var giftStr:String = "";
      
      private var diceArr:Array = [];
      
      private var dicePointArr:Array = [];
      
      public function ZhongQiuBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var mc0:MovieClip = null;
         var point0:Point = null;
         super.setImg(img0);
         this.infoSp.visible = false;
         this.infoSp["closeBtn"].addEventListener(MouseEvent.CLICK,this.infoCloseClick);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
         this.startBtn.actived = false;
         this.startBtn.setName("博一把");
         this.infoBtn.setName("规则");
         this.shopBtn.setName("兑换物品");
         var diceChildNum0:int = this.diceSp.numChildren;
         for(var i:int = 0; i < diceChildNum0; i++)
         {
            mc0 = this.diceSp.getChildAt(i) as MovieClip;
            if(Boolean(mc0))
            {
               this.diceArr.push(mc0);
               mc0.gotoAndStop(2);
               point0 = new Point(mc0.x,mc0.y);
               this.dicePointArr.push(point0);
            }
         }
         addChild(this.infoSp);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         this.fleshTime();
      }
      
      private function get SAVE() : ZhongQiuSave
      {
         return Gaming.PG.save.gift.zhongQiu24;
      }
      
      private function get nowActive() : int
      {
         return Gaming.PG.da.active.nowActive;
      }
      
      public function outLoginEvent() : void
      {
         this.giftStr = "";
      }
      
      private function fleshTime() : void
      {
         var day0:int = 0;
         Gaming.uiGroup.connectUI.hide();
         var timeStr0:String = Gaming.api.save.nowSeverTime;
         var startTime0:String = ZhongQiuSave.START_TIME;
         var endTime0:String = ZhongQiuSave.END_TIME;
         var tip0:String = "";
         if(StringDate.compareDateByStr(startTime0,timeStr0) < 0)
         {
            tip0 = "活动还未开始！";
         }
         else
         {
            day0 = StringDate.compareDateByStr(timeStr0,endTime0);
            if(day0 >= 0)
            {
               this.timeTxt.htmlText = "活动时间还剩余" + (day0 + 1) + "天";
            }
            else
            {
               tip0 = "活动已结束！";
            }
         }
         if(tip0 != "")
         {
            this.timeTxt.htmlText = tip0;
            tip0 += "\n开始时间：" + startTime0;
            tip0 += "\n结束时间：" + endTime0;
            Gaming.uiGroup.alertBox.showError(tip0);
            hide();
         }
         else
         {
            super.show();
            this.flesh();
         }
      }
      
      private function flesh() : void
      {
         this.giftTxt.htmlText = this.giftStr;
         var canNum0:int = this.SAVE.getCanNum(this.nowActive);
         this.numTxt.htmlText = "今日剩余" + canNum0 + "次机会";
         this.startBtn.actived = canNum0 > 0;
      }
      
      private function startDice() : void
      {
         UIOrder.getStoreState(this.afterStart);
      }
      
      private function afterStart(v0:int) : void
      {
         var diceObj0:Object = null;
         var diceArr0:Array = null;
         var d0:DicePackDefine = null;
         var giftD0:GiftAddDefineGroup = null;
         if(v0 == 1 || v0 == -2)
         {
            Gaming.uiGroup.connectUI.hide();
            diceObj0 = this.SAVE.getDiceObj();
            diceArr0 = diceObj0["diceArr"];
            d0 = diceObj0["def"];
            giftD0 = d0.getGift();
            GiftAddit.add(giftD0);
            this.showDice(diceArr0);
            this.giftStr = d0.getTipStr();
            this.flesh();
            UIOrder.save(true,false,false);
         }
      }
      
      private function showDice(arr0:Array) : void
      {
         var mc0:MovieClip = null;
         var num0:int = 0;
         var p0:Point = null;
         for(var i:int = 0; i < this.diceArr.length; i++)
         {
            mc0 = this.diceArr[i];
            num0 = int(arr0[i]);
            p0 = this.dicePointArr[i];
            mc0.gotoAndStop(num0);
            mc0.x = p0.x + NumberMethod.getRandom(-5,5);
            mc0.y = p0.y + NumberMethod.getRandom(-5,5);
            mc0.rotation = NumberMethod.getRandom(-10,10);
         }
      }
      
      private function tipOver(e:MouseEvent) : void
      {
         var s0:String = "1、每天登录有5次掷骰子机会。";
         s0 += "\n2、在活跃值任务中，每获得15点活跃值还会获得1次掷骰子机会，每天最多获得10次。";
         Gaming.uiGroup.tipBox.textTip.showFollowText(s0);
      }
      
      private function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = btn0.label + "BtnClick";
         this[funName0](e);
      }
      
      private function startBtnClick(e:MouseEvent) : void
      {
         this.startDice();
      }
      
      private function infoBtnClick(e:MouseEvent) : void
      {
         this.infoSp.visible = true;
      }
      
      private function infoCloseClick(e:MouseEvent) : void
      {
         this.infoSp.visible = false;
      }
      
      private function shopBtnClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.shopUI.gotoLabel(PriceType.PUMPKIN);
      }
      
      private function tipBtnClick(e:MouseEvent) : void
      {
      }
   }
}

