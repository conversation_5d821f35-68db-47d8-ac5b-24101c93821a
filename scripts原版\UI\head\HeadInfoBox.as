package UI.head
{
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGridIcon;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.head.HeadTempData;
   import dataAll._app.head.define.HeadDefine;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.ui.GatherColor;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class HeadInfoBox extends NormalUI
   {
      
      private var baseTxt:TextField;
      
      private var baseValueTxt:TextField;
      
      private var addTxt:TextField;
      
      private var addValueTxt:TextField;
      
      private var condtionTxt:TextField;
      
      private var proTitleTxt:TextField;
      
      private var useBtnSp:MovieClip;
      
      private var noBtnSp:MovieClip;
      
      private var topBtnSp:MovieClip;
      
      private var iconTag:Sprite;
      
      private var icon:NormalGridIcon = new NormalGridIcon();
      
      private var useBtn:NormalBtn = new NormalBtn();
      
      private var noBtn:NormalBtn = new NormalBtn();
      
      private var topBtn:NormalBtn = new NormalBtn();
      
      private var nowDefine:HeadDefine;
      
      public var headUI:HeadUI;
      
      public function HeadInfoBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["noBtnSp","iconTag","baseTxt","baseValueTxt","addTxt","addValueTxt","condtionTxt","proTitleTxt","useBtnSp","topBtnSp"];
         super.setImg(img0);
         FontDeal.dealLine(this.baseTxt);
         FontDeal.dealLine(this.baseValueTxt);
         FontDeal.dealLine(this.addTxt);
         FontDeal.dealLine(this.addValueTxt);
         FontDeal.dealOne(this.proTitleTxt);
         addChild(this.useBtn);
         this.useBtn.setImg(this.useBtnSp);
         this.useBtn.addEventListener(MouseEvent.CLICK,this.useBtnClick);
         addChild(this.noBtn);
         this.noBtn.setImg(this.noBtnSp);
         this.noBtn.addEventListener(MouseEvent.CLICK,this.noBtnClick);
         addChild(this.topBtn);
         this.topBtn.setImg(this.topBtnSp);
         this.topBtn.addEventListener(MouseEvent.CLICK,this.topBtnClick);
         addChild(this.icon);
         this.icon.x = this.iconTag.x;
         this.icon.y = this.iconTag.y;
         this.noData();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function outLoginEvent() : void
      {
         this.nowDefine = null;
         this.noData();
      }
      
      public function stopAll() : void
      {
         this.icon.stopAll();
      }
      
      public function playAll() : void
      {
         this.icon.playAll();
      }
      
      public function noData() : void
      {
         this.visible = false;
         this.icon.clearData();
         this.baseTxt.htmlText = "";
         this.baseValueTxt.htmlText = "";
         this.condtionTxt.htmlText = "";
         this.addTxt.text = "";
         this.addValueTxt.text = "";
         this.useBtn.visible = false;
         this.noBtn.visible = false;
      }
      
      public function inData(t0:HeadTempData) : void
      {
         this.visible = true;
         var d0:HeadDefine = t0.define;
         this.nowDefine = t0.define;
         this.icon.setIconName(d0.iconUrl);
         this.baseTxt.htmlText = "可获得荣誉值\n解锁等级\n" + t0.getLifeTitleStr();
         this.baseValueTxt.htmlText = d0.getHonorValue() + "点\n" + d0.getUnlockStr(Gaming.PG.da.level) + "\n" + t0.getLifeValueStr();
         var proTitle0:String = "提升属性";
         if(d0.noEquip)
         {
            proTitle0 += ComMethod.color("(无需使用也可生效)",GatherColor.orangeColor);
         }
         this.proTitleTxt.htmlText = proTitle0;
         this.inData_property(t0);
         this.condtionTxt.htmlText = FontDeal.getDealLeadingStr(this.condtionTxt,t0.getConditionStr());
         this.useBtn.visible = false;
         this.noBtn.visible = false;
         this.topBtn.visible = false;
         if(t0.completeB)
         {
            this.useBtn.visible = !t0.isNowB;
            this.noBtn.visible = !this.useBtn.visible;
            this.topBtn.visible = true;
         }
         this.useBtn.actived = !t0.isNowB;
         this.useBtn.setName("使用称号");
         this.noBtn.setName("卸下称号");
      }
      
      private function inData_property(t0:HeadTempData) : void
      {
         var name0:* = undefined;
         var v0:Number = NaN;
         var proD0:PropertyArrayDefine = null;
         var d0:HeadDefine = t0.define;
         var proObj0:Object = d0.getAddObj();
         var titleStr0:String = "";
         var valueStr0:String = "";
         for(name0 in proObj0)
         {
            v0 = Number(proObj0[name0]);
            proD0 = Gaming.defineGroup.getPropertyArrayDefine(name0);
            titleStr0 += proD0.cnName + "\n";
            valueStr0 += proD0.getValueString(v0) + "\n";
         }
         this.addTxt.text = titleStr0;
         this.addValueTxt.text = valueStr0;
      }
      
      private function useBtnClick(e:MouseEvent) : void
      {
         if(Boolean(this.nowDefine))
         {
            Gaming.PG.da.head.useHead(this.nowDefine.name);
            Gaming.PG.da.fleshAllByEquip();
            this.headUI.fleshData();
         }
      }
      
      private function noBtnClick(e:MouseEvent) : void
      {
         if(Boolean(this.nowDefine))
         {
            Gaming.PG.da.head.unloadHead(this.nowDefine.name);
            Gaming.PG.da.fleshAllByEquip();
            this.headUI.fleshData();
         }
      }
      
      private function topBtnClick(e:MouseEvent) : void
      {
         var bb0:Boolean = false;
         if(Boolean(this.nowDefine))
         {
            bb0 = Gaming.PG.da.head.save.topHead(this.nowDefine.name);
            if(bb0)
            {
               this.headUI.fleshData();
            }
         }
      }
   }
}

