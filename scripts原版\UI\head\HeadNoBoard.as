package UI.head
{
   import UI.bag.ItemsGripBox;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import dataAll._app.head.HeadTempData;
   import flash.display.Sprite;
   
   public class HeadNoBoard extends NormalUI
   {
      
      private var pageTag:Sprite;
      
      private var gripTag:Sprite;
      
      public var itemsBox:ItemsGripBox = new ItemsGripBox();
      
      public var nowChooseName:String = "";
      
      public var infoBox:HeadInfoBox;
      
      public function HeadNoBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["gripTag","pageTag"];
         super.setImg(img0);
         addChild(this.itemsBox);
         this.itemsBox.setIconPro("HeadUI/headGrip");
         this.itemsBox.arg.init(3,6,4,4);
         this.itemsBox.evt.setWantEvent(true,false,false,true,true);
         this.itemsBox.x = this.gripTag.x;
         this.itemsBox.y = this.gripTag.y;
         this.itemsBox.pageBox.setToNormalBtn();
         this.itemsBox.pageBox.setXY_bySp(this.pageTag,this.itemsBox);
         this.itemsBox.addEventListener(ClickEvent.ON_CLICK,this.barClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function outLoginEvent() : void
      {
         this.nowChooseName = null;
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshList();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      public function stopAll() : void
      {
         this.itemsBox.stopAll();
      }
      
      public function playAll() : void
      {
         this.itemsBox.playAll();
      }
      
      protected function getList() : Array
      {
         return Gaming.PG.da.head.getNoTempDataArr(Gaming.api.save.getNowServerDate().getStr());
      }
      
      private function fleshList() : void
      {
         var arr0:Array = this.getList();
         this.itemsBox.inData_byArr(arr0,"inData_headTempData");
         this.itemsBox.setChoose(this.nowChooseName);
         var da0:HeadTempData = null;
         var btn0:NormalBtn = this.itemsBox.getBtnByLabel(this.nowChooseName);
         if(Boolean(btn0))
         {
            da0 = btn0.itemsData as HeadTempData;
         }
         this.showData(da0);
      }
      
      private function barClick(e:ClickEvent) : void
      {
         var t0:HeadTempData = e.childData as HeadTempData;
         this.showData(t0);
      }
      
      protected function showData(t0:HeadTempData) : void
      {
         if(Boolean(t0))
         {
            this.nowChooseName = t0.define.name;
            this.infoBox.inData(t0);
            this.itemsBox.setChoose(this.nowChooseName);
         }
         else
         {
            this.infoBox.noData();
         }
      }
   }
}

