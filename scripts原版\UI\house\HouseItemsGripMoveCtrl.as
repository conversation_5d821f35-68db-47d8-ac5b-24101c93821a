package UI.house
{
   import UI.bag.ItemsGripBtnListCtrl;
   import UI.bag.ItemsGripMoveCtrl;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.house.HouseDataCtrl;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   
   public class HouseItemsGripMoveCtrl
   {
      
      private static var HOUSE_DG:ItemsDataGroup = null;
      
      private static var BAG_DG:ItemsDataGroup = null;
      
      private static var HOUSE_SITE:int = 0;
      
      private static var BAG_SITE:int = 0;
      
      public function HouseItemsGripMoveCtrl()
      {
         super();
      }
      
      public static function pan(dg1:ItemsDataGroup, dg2:ItemsDataGroup, site1:int, site2:int) : Boolean
      {
         var houseDg:ItemsDataGroup = null;
         var bagDg:ItemsDataGroup = null;
         var houseSite:int = 0;
         var bagSite:int = 0;
         var houseDa:IO_ItemsData = null;
         var bagDa:IO_ItemsData = null;
         var canSwapB0:Boolean = false;
         var mustD0:MustDefine = null;
         var tipStr0:String = null;
         var continueB0:Boolean = true;
         if(dg1.placeType != dg2.placeType)
         {
            houseDg = dg1;
            bagDg = dg2;
            houseSite = site1;
            bagSite = site2;
            if(dg2.placeType == ItemsDataGroup.PLACE_HOUSE)
            {
               houseDg = dg2;
               bagDg = dg1;
               houseSite = site2;
               bagSite = site1;
            }
            houseDa = houseDg.getDataBySite(houseSite);
            bagDa = bagDg.getDataBySite(bagSite);
            canSwapB0 = canSwapPan(houseDa,bagDa);
            if(!canSwapB0)
            {
               continueB0 = false;
               Gaming.uiGroup.alertBox.showError("不能把此物品放入仓库。");
            }
            else if(houseDa is IO_ItemsData)
            {
               mustD0 = getCoinMust(houseDa);
               if(mustD0.coin > 0)
               {
                  HOUSE_DG = houseDg;
                  BAG_DG = bagDg;
                  HOUSE_SITE = houseSite;
                  BAG_SITE = bagSite;
                  tipStr0 = "从仓库中取出 " + houseDa.getSave().getColorCnName() + " ，需要：\n";
                  tipStr0 += mustD0.getText() + " \n是否继续？";
                  Gaming.uiGroup.alertBox.showNormal(tipStr0,"yesAndNo",yesOut);
                  Gaming.uiGroup.alertBox.yesBtn.actived = mustD0.panCondition();
                  continueB0 = false;
               }
            }
            if(bagDa is IO_ItemsData && continueB0)
            {
               bagDa.getSave().setInHouseTime(Gaming.PG.da.time.getReadTimeDate().getStr());
            }
         }
         return continueB0;
      }
      
      private static function canSwapPan(da1:IO_ItemsData, da2:IO_ItemsData) : Boolean
      {
         if(Boolean(da1))
         {
            if(!canSwapOnePan(da1))
            {
               return false;
            }
         }
         if(Boolean(da2))
         {
            if(!canSwapOnePan(da2))
            {
               return false;
            }
         }
         return true;
      }
      
      private static function canSwapOnePan(da0:IO_ItemsData) : Boolean
      {
         return true;
      }
      
      private static function yesOut() : void
      {
         var houseDa:IO_ItemsData = HOUSE_DG.getDataBySite(HOUSE_SITE);
         var mustD0:MustDefine = getCoinMust(houseDa);
         PlayerMustCtrl.deductMust(mustD0,affterOut);
      }
      
      private static function affterOut() : void
      {
         Gaming.soundGroup.playSound("uiSound","changeLabel");
         var houseDa:IO_ItemsData = HOUSE_DG.getDataBySite(HOUSE_SITE);
         houseDa.getSave().setInHouseTime("");
         var bagDa:IO_ItemsData = BAG_DG.getDataBySite(BAG_SITE);
         if(bagDa is IO_ItemsData)
         {
            bagDa.getSave().setInHouseTime(Gaming.PG.da.time.getReadTimeDate().getStr());
         }
         ItemsGripMoveCtrl.swap(HOUSE_DG,BAG_DG,HOUSE_SITE,BAG_SITE);
         ItemsGripBtnListCtrl.fleshAllBy(BAG_DG);
      }
      
      private static function getCoinMust(da0:IO_ItemsData) : MustDefine
      {
         var coin0:int = HouseDataCtrl.countCoin(da0,Gaming.PG.da.time.getReadTimeDate());
         var d0:MustDefine = new MustDefine();
         d0.coin = coin0;
         return d0;
      }
   }
}

