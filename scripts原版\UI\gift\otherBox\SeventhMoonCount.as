package UI.gift.otherBox
{
   import UI.base.tip.TextGatherAnalyze;
   import dataAll._player.PlayerData;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.save.GiftSave;
   
   public class SeventhMoonCount
   {
      
      public var gift:GiftAddDefineGroup = null;
      
      public var tip:String = "";
      
      public function SeventhMoonCount()
      {
         super();
      }
      
      private static function get giftSave() : GiftSave
      {
         return Gaming.PG.save.gift;
      }
      
      private static function get mainPD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      public static function sevenGiftB() : Boolean
      {
         return true;
      }
      
      private static function sevenGiftEvent(cn0:String) : void
      {
      }
      
      public static function isUseB(cn0:String) : Bo<PERSON>an
      {
         return true;
      }
      
      private static function countValue(uid0:String, cn0:String) : Number
      {
         var u0:Number = Number(uid0);
         var c0:Number = toCode(cn0);
         c0 += u0;
         return (c0 + 75) % 100;
      }
      
      private static function toCode(str:String) : Number
      {
         var ss:String = null;
         var v0:Number = 0;
         var len:int = str.length;
         for(var n:int = 0; n <= len - 1; n++)
         {
            ss = str.substr(n,1);
            v0 += ss.charCodeAt();
         }
         return v0;
      }
      
      private static function getInfo(v0:Number) : String
      {
         var arr0:Array = [];
         arr0.push("你们如同冰与火，永不相容。");
         arr0.push("你们一生不会有任何交集。");
         arr0.push("你们将有一面之缘，仅此而已。");
         arr0.push("你们只是普通朋友。");
         arr0.push("你们将成为好朋友，相互尊重。");
         arr0.push("你们彼此有着吸引力，但最终没能在一起。");
         arr0.push("你们的情路会有波折，分分合合。");
         arr0.push("你们是彼此深爱的一对，但无法携手走入婚姻殿堂。");
         arr0.push("你们将结为夫妻，平淡地度过一生。");
         arr0.push("你们将儿孙满堂，白头偕老！");
         var index0:int = v0 / 10;
         if(index0 > arr0.length - 1)
         {
            index0 = arr0.length - 1;
         }
         if(index0 < 0)
         {
            index0 = 0;
         }
         return arr0[index0];
      }
      
      public static function count(uid0:String, cn0:String) : SeventhMoonCount
      {
         var g0:GiftAddDefineGroup = null;
         var s0:SeventhMoonCount = new SeventhMoonCount();
         var v0:Number = countValue(uid0,cn0);
         var n0:String = "<green " + mainPD.base.save.playerName + "/>";
         n0 += "和<green " + cn0 + "/>的缘分指数为：";
         n0 += "<b><yellow " + v0 + "/></b>。";
         n0 += "\n<orange " + getInfo(v0) + "/>";
         n0 = TextGatherAnalyze.swapText(n0);
         if(!sevenGiftB())
         {
            g0 = new GiftAddDefineGroup();
            g0.addGiftByStr("things;lily;" + Math.round(v0 / 100 * 2 + 2));
            g0.addGiftByStr("base;anniCoin;" + Math.round((v0 / 100 * 2 + 3) * 3));
            g0.addGiftByStr("base;tenCoin;" + Math.round((v0 / 100 * 2 + 3) * 1.5));
            s0.gift = g0;
            n0 += "\n今日测试奖励如下：\n" + g0.getDescription();
            sevenGiftEvent(cn0);
            GiftAddit.add(g0,"");
         }
         s0.tip = n0;
         return s0;
      }
   }
}

