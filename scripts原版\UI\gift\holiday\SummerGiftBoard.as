package UI.gift.holiday
{
   import UI.UIOrder;
   import UI.api.shop.ShopBuyObject;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.loadBar.LoadBar;
   import com.sounto.oldUtils.StringDate;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.save.SummerGiftSave;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class SummerGiftBoard extends AutoNormalUI
   {
      
      public var closeFun:Function = null;
      
      private var pointSp:Sprite;
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      private var loadBar:LoadBar;
      
      private var timeTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var tipStr:String = "";
      
      private var overB:Boolean = false;
      
      public function SummerGiftBoard()
      {
         super();
         btnSetB = false;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var btn0:NormalBtn = null;
         super.setImg(img0);
         var getBtn0:NormalBtn = btnObj["get"];
         ItemsGripTipCtrl.addNormalBtnTip(getBtn0);
         this.infoTxt.text = "充值返利优惠：7月1日~8月31日期间，当天充值任意金额次日返利20%。";
         for each(btn0 in btnObj)
         {
            if(btn0.label.indexOf("summer") >= 0)
            {
               btn0.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
               btn0.addEventListener(MouseEvent.MOUSE_OUT,this.btnOut);
            }
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      protected function get SAVE() : SummerGiftSave
      {
         return Gaming.PG.save.gift.summer25;
      }
      
      override public function show() : void
      {
         var getBtn0:NormalBtn = btnObj["get"];
         getBtn0.actived = false;
         this.afterGetTime(Gaming.PG.da.time.getReadTime());
      }
      
      private function afterGetTime(str0:String) : void
      {
         var day0:int = 0;
         this.overB = false;
         var sd0:StringDate = new StringDate(str0);
         var f0:int = sd0.betweenIn(SummerGiftSave.getStartTime(),SummerGiftSave.getOverTime());
         if(f0 == 0)
         {
            day0 = StringDate.compareDateByStr(str0,SummerGiftSave.getOverTime());
            this.timeTxt.htmlText = "距离活动结束还剩余" + day0 + "天";
            this.fleshData();
         }
         else if(f0 == 1)
         {
            this.overB = true;
            this.timeTxt.htmlText = "活动已结束";
            this.fleshData();
         }
         else
         {
            Gaming.uiGroup.alertBox.showError(f0 == -1 ? "活动开始时间：" + SummerGiftSave.getStartTime() : "当前活动已结束！");
            this.closeFun();
         }
      }
      
      private function noGetTime(v0:* = null) : void
      {
         Gaming.uiGroup.connectUI.hide();
         this.closeFun();
      }
      
      private function fleshData() : void
      {
         var g0:GiftAddDefineGroup = null;
         var dailyB:Boolean = false;
         var haveGiftB0:Boolean = false;
         var lastGetB0:Boolean = false;
         var getBtn0:NormalBtn = null;
         var dailyBtn0:NormalBtn = null;
         var fillBtn0:NormalBtn = null;
         var must0:int = 0;
         var bb0:Boolean = false;
         var giftB0:Boolean = false;
         var btn0:NormalBtn = null;
         var name0:String = null;
         var icon0:String = null;
         var giftSave0:SummerGiftSave = this.SAVE;
         var gArr0:Array = giftSave0.getGiftArr();
         for each(g0 in gArr0)
         {
            must0 = g0.mustLevel;
            bb0 = giftSave0.num >= must0;
            giftB0 = giftSave0.getGiftGetB(must0 + "");
            btn0 = btnObj[g0.name];
            name0 = must0 + "天";
            icon0 = "AnniverUI/gift2";
            if(g0.name == "summer1")
            {
               name0 = "每日";
               icon0 = "AnniverUI/gift1";
            }
            btn0.setName(name0);
            btn0.setIconName(icon0);
            if(bb0)
            {
               btn0.setNew(!giftB0);
            }
            else
            {
               btn0.setNew(false);
            }
            btn0.mouseIconEffectB = bb0;
            btn0.activedAndEnabled = false;
            btn0.activedAndGray = true;
            btn0.actived = bb0;
         }
         dailyB = giftSave0.dailyB;
         haveGiftB0 = giftSave0.getGift().arr.length > 0;
         lastGetB0 = giftSave0.getLastGetB();
         getBtn0 = btnObj["get"];
         dailyBtn0 = btnObj["daily"];
         fillBtn0 = btnObj["fill"];
         fillBtn0.setName("补签");
         dailyBtn0.actived = !dailyB && this.overB == false;
         dailyBtn0.setName(!dailyB ? "签到" : "今日已签到");
         getBtn0.actived = haveGiftB0;
         getBtn0.setName(!haveGiftB0 && dailyB ? "今日已领取" : "领取礼包");
         getBtn0.tipString = giftSave0.getGiftBtnTip();
         this.setPoint(giftSave0.num);
      }
      
      private function setPoint(day0:int) : void
      {
         var g0:GiftAddDefineGroup = null;
         var nextG0:GiftAddDefineGroup = null;
         this.pointSp["txt"].text = "已签到" + day0 + "天";
         var gArr0:Array = this.SAVE.getGiftArr();
         var allNum0:int = int(gArr0.length);
         var per0:Number = 1;
         for(var i:int = 0; i < allNum0; i++)
         {
            g0 = gArr0[i];
            nextG0 = gArr0[i + 1];
            if(!Boolean(nextG0))
            {
               break;
            }
            if(day0 >= g0.mustLevel && day0 < nextG0.mustLevel)
            {
               per0 = 1 / (allNum0 - 1) * (i + (day0 - g0.mustLevel) / (nextG0.mustLevel - g0.mustLevel));
               break;
            }
         }
         if(day0 == 0)
         {
            per0 = 0;
         }
         this.loadBar.setPer(per0);
         this.pointSp.x = this.loadBar.x + this.loadBar.width * per0;
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var gift0:GiftAddDefineGroup = null;
         var bagstr0:String = null;
         var giftSave0:SummerGiftSave = this.SAVE;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.label == "close")
         {
            this.closeFun();
         }
         else if(btn0.label == "fill")
         {
            this.fillClick(null);
         }
         else if(btn0.label == "daily")
         {
            giftSave0.daily();
            this.fleshData();
            Gaming.uiGroup.alertBox.showSuccess("签到成功！");
         }
         else if(btn0.label == "get")
         {
            gift0 = giftSave0.getGift();
            bagstr0 = GiftAddit.bagSpacePan(gift0);
            if(bagstr0 != "")
            {
               Gaming.uiGroup.alertBox.showError(bagstr0);
            }
            else
            {
               giftSave0.getGiftEvent();
               GiftAddit.add(gift0,"");
               this.tipStr = "领取礼包成功！获得：\n" + gift0.getDescription(3);
               UIOrder.save(true,true,false,this.afterGetGift);
            }
         }
      }
      
      private function afterGetGift(v:* = null) : void
      {
         Gaming.uiGroup.alertBox.showSuccess(this.tipStr);
         this.fleshData();
         Gaming.uiGroup.mainUI.fleshBtn();
      }
      
      private function btnOver(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var g0:GiftAddDefineGroup = Gaming.defineGroup.gift.getOne(btn0.label);
         Gaming.uiGroup.giftTip.showTip(g0,btn0);
      }
      
      private function btnOut(e:MouseEvent) : void
      {
      }
      
      private function fillClick(e:MouseEvent) : void
      {
         var da0:GoodsData = this.getFillDailyGoodsData();
         if(da0.getMaxNumLimit() <= 0)
         {
            Gaming.uiGroup.alertBox.showError("今天已经不需要补签。");
         }
         else
         {
            Gaming.uiGroup.alertBox.shop.showCheck(da0,this.yes_fill);
         }
      }
      
      private function yes_fill() : void
      {
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var price0:Number = da0.getPrice();
         var shopObj0:ShopBuyObject = da0.getShopObj();
         Gaming.uiGroup.connectUI.show();
         Gaming.api.shop.buyPropNd(shopObj0,this.do_fill);
      }
      
      private function do_fill() : void
      {
         Gaming.uiGroup.connectUI.hide();
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var giftSave0:SummerGiftSave = this.SAVE;
         giftSave0.fill(da0.nowNum);
         Gaming.soundGroup.playSound("uiSound","changeLabel");
         this.fleshData();
      }
      
      private function getFillDailyGoodsData() : GoodsData
      {
         var giftSave0:SummerGiftSave = this.SAVE;
         var da0:GoodsData = new GoodsData();
         var d0:GoodsDefine = Gaming.defineGroup.goods.getDefine("summerFillDaily");
         da0.def = d0;
         da0.playerData = Gaming.PG.da;
         da0.showTextType = "daily";
         da0.setMaxNumLimit(giftSave0.getFillNum(Gaming.PG.da.time.getReadTime(),this.overB));
         return da0;
      }
   }
}

