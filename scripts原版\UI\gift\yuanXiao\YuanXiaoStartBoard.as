package UI.gift.yuanXiao
{
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class YuanXiaoStartBoard extends AutoNormalUI
   {
      
      private var startBtn:NormalBtn;
      
      private var infoTxt:TextField;
      
      public function YuanXiaoStartBoard()
      {
         super();
         btnSetB = true;
         mcTypeArr = ["txt","btnSp"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.startBtn.setName("开始游戏");
         FontDeal.dealLine(this.infoTxt);
         this.infoTxt.htmlText = FontDeal.getDealLeadingStr(this.infoTxt,YuanXiaoUI.getTip());
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var e0:ClickEvent = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.startBtn && btn0.actived)
         {
            e0 = new ClickEvent(ClickEvent.ON_CLICK);
            dispatchEvent(e0);
         }
      }
      
      public function showData(timeOverB0:Boolean = false) : void
      {
         show();
         if(timeOverB0)
         {
            this.startBtn.actived = false;
            this.startBtn.setName("活动已结束");
         }
         else
         {
            this.startBtn.actived = true;
            this.startBtn.setName("开始游戏");
         }
      }
   }
}

