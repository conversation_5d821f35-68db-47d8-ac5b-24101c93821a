package UI.gift.yuanXiao
{
   import UI.base.event.ClickEvent;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.pool.OnePool;
   import com.sounto.utils.ArrayMethod;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.Sprite;
   import flash.events.EventDispatcher;
   import flash.events.MouseEvent;
   
   public class YuanXiaoGame extends EventDispatcher
   {
      
      public static const FIRST_X:int = 150;
      
      private static const NO:String = "no";
      
      private static const ING:String = "ing";
      
      private static const FLYING:String = "flying";
      
      private static const OVER:String = "over";
      
      private var initB:Boolean = false;
      
      private var ball:YuanBall;
      
      private var firstBowl:YuanBowl;
      
      private var nextBowl:YuanBowl;
      
      private var bowlPool:OnePool = new OnePool();
      
      private var bowlArr:Array = [];
      
      private var arrowSp:Sprite;
      
      private var state:String = "";
      
      private var mouseDownB:Boolean = false;
      
      private var con:YuanXiaoCon = new YuanXiaoCon();
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var allGift:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      public function YuanXiaoGame()
      {
         super();
         this.bowlNum = 0;
         this.bowlIndex = 0;
      }
      
      public function get bowlIndex() : Number
      {
         return this.CF.getAttribute("bowlIndex");
      }
      
      public function set bowlIndex(v0:Number) : void
      {
         this.CF.setAttribute("bowlIndex",v0);
      }
      
      public function get bowlNum() : Number
      {
         return this.CF.getAttribute("bowlNum");
      }
      
      public function set bowlNum(v0:Number) : void
      {
         this.CF.setAttribute("bowlNum",v0);
      }
      
      public function testBowlIndex(v0:int) : void
      {
         this.bowlIndex = v0;
      }
      
      public function init(con0:Sprite) : void
      {
         var i:int = 0;
         var b0:YuanBowl = null;
         if(!this.initB)
         {
            this.con.init(con0);
            this.arrowSp = Gaming.swfLoaderManager.getResourceFull("YuanXiaoUI/arrow");
            this.con.ball.addChild(this.arrowSp);
            this.initB = true;
            this.bowlPool.maxNum = 5;
            for(i = 0; i < this.bowlPool.maxNum; i++)
            {
               b0 = new YuanBowl();
               b0.initImg();
               this.bowlPool.addObject(b0);
            }
         }
      }
      
      public function start() : void
      {
         this.clear();
         this.bowlNum = 0;
         this.bowlIndex = 0;
         this.con.frontToFront();
         this.addBall();
         this.addFirstBowl(this.ball);
         this.nextBowl = this.addNewNextBowl();
         this.addNewNextBowl();
         this.addNewNextBowl();
         this.state = ING;
      }
      
      public function clear() : void
      {
         this.bowlNum = 0;
         this.allGift.clearAll();
         this.removeBall();
         this.removeAllBowl();
         this.state = NO;
         this.mouseDownB = false;
      }
      
      private function next() : void
      {
         this.firstBowl = this.nextBowl;
         var nextIndex0:int = int(this.bowlArr.indexOf(this.nextBowl));
         this.nextBowl = this.bowlArr[nextIndex0 + 1];
         this.removeBowl(this.bowlArr[0]);
         this.addNewNextBowl();
      }
      
      private function addBall() : void
      {
         if(!this.ball)
         {
            this.ball = new YuanBall();
            this.ball.init(this.con.ball);
         }
      }
      
      private function removeBall() : void
      {
         if(Boolean(this.ball))
         {
            this.ball.removeMe();
            this.ball = null;
         }
      }
      
      public function mouseDown(e:MouseEvent) : void
      {
         if(this.state == ING)
         {
            this.mouseDownB = true;
         }
      }
      
      public function mouseUp(e:MouseEvent) : void
      {
         if(this.mouseDownB)
         {
            if(this.state == ING)
            {
               Gaming.soundGroup.playAndAdd("YuanXiaoUI/shoot");
               this.ball.shoot();
               this.con.ballToFront();
               this.state = FLYING;
            }
         }
         this.mouseDownB = false;
      }
      
      private function addBowl() : YuanBowl
      {
         var b0:YuanBowl = this.bowlPool.getObject() as YuanBowl;
         ++this.bowlIndex;
         b0.init(this.con,this.bowlIndex);
         this.bowlArr.push(b0);
         return b0;
      }
      
      private function removeBowl(b0:YuanBowl) : void
      {
         b0.removeMe();
         this.bowlPool.addObject(b0);
         ArrayMethod.remove(this.bowlArr,b0);
      }
      
      private function removeAllBowl() : void
      {
         var b0:YuanBowl = null;
         for each(b0 in this.bowlArr)
         {
            b0.removeMe();
            this.bowlPool.addObject(b0);
         }
         this.bowlArr.length = 0;
         this.firstBowl = null;
         this.nextBowl = null;
      }
      
      private function addFirstBowl(ball0:YuanBall) : void
      {
         var b0:YuanBowl = this.addBowl();
         b0.x = FIRST_X;
         b0.setMiddle(ball0);
         this.firstBowl = b0;
      }
      
      private function addNewNextBowl() : YuanBowl
      {
         var last0:YuanBowl = this.bowlArr[this.bowlArr.length - 1];
         var b0:YuanBowl = this.addBowl();
         b0.x = last0.x + this.getRanGap();
         return b0;
      }
      
      private function getRanGap() : int
      {
         var range0:int = 100;
         var nowNum0:int = this.bowlNum;
         if(nowNum0 < 5)
         {
            range0 = 0;
         }
         else if(nowNum0 < 10)
         {
            range0 = 50;
         }
         else if(nowNum0 < 20)
         {
            range0 = 200;
         }
         else if(nowNum0 < 40)
         {
            range0 = 300;
         }
         else if(nowNum0 < 60)
         {
            range0 = 500;
         }
         var gap0:int = (0.5 - Math.random()) * range0 + 300;
         if(gap0 < 160)
         {
            gap0 = 160;
         }
         if(gap0 > 500)
         {
            gap0 = 500;
         }
         return gap0;
      }
      
      private function noTimer() : void
      {
      }
      
      private function ingTimer() : void
      {
         if(this.mouseDownB)
         {
            this.ball.addAccTime();
            this.arrowSp.visible = true;
            this.arrowSp.width = this.ball.getArrawLen();
            this.arrowSp.rotation = this.ball.getShootRa() * 180 / Math.PI;
            this.arrowSp.x = this.ball.x;
            this.arrowSp.y = this.ball.y;
         }
         else
         {
            this.arrowSp.visible = false;
         }
      }
      
      private function flyingTimer() : void
      {
         var e0:ClickEvent = null;
         var gift0:GiftAddDefine = null;
         this.ball.motionCount();
         if(this.ball.isFloorB())
         {
            this.state = OVER;
            e0 = new ClickEvent(ClickEvent.ON_OVER);
            if(this.allGift.arr.length == 0)
            {
               e0.index = this.bowlNum;
            }
            dispatchEvent(e0);
         }
         else if(this.nextBowl.hit(this.ball.x,this.ball.y))
         {
            this.state = ING;
            ++this.bowlNum;
            this.ball.y = this.nextBowl.getHitY();
            this.ball.hitEvent();
            gift0 = this.nextBowl.hitEvent();
            this.allGift.mergeOne(gift0);
            this.con.frontToFront();
            this.next();
         }
      }
      
      private function overTimer() : void
      {
      }
      
      public function FTimer() : void
      {
         if(Boolean(this.ball) && Boolean(this.nextBowl))
         {
            this.arrowSp.visible = false;
            this[this.state + "Timer"]();
            this.con.setBallX(this.ball.x);
         }
      }
   }
}

