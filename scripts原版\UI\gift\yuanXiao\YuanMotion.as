package UI.gift.yuanXiao
{
   public class Yuan<PERSON>otion
   {
      
      protected var _x:Number = 0;
      
      protected var _y:Number = 0;
      
      public function YuanMotion()
      {
         super();
      }
      
      public function set x(value:Number) : void
      {
         this._x = value;
      }
      
      public function get x() : Number
      {
         return this._x;
      }
      
      public function set y(value:Number) : void
      {
         this._y = value;
      }
      
      public function get y() : Number
      {
         return this._y;
      }
   }
}

