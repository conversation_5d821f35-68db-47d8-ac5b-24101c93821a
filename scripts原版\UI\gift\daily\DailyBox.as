package UI.gift.daily
{
   import UI.base.box.NormalBox;
   import UI.base.button.NormalBtn;
   import com.sounto.oldUtils.StringDate;
   
   public class DailyBox extends NormalBox
   {
      
      public function DailyBox()
      {
         super();
      }
      
      public function inData_byDaily(firstIndex0:int, len0:int, nowDate0:StringDate, dateArr0:Array) : Boolean
      {
         var n:* = undefined;
         var btn0:NormalBtn = null;
         var index0:int = 0;
         var day0:int = 0;
         var date2:StringDate = null;
         var date_str2:String = null;
         setGripNum(7 * 6,imgType);
         var todaySignB:Boolean = false;
         for(n in gripArr)
         {
            btn0 = gripArr[n];
            btn0.setNew(false);
            btn0.actived = false;
            index0 = n - firstIndex0;
            if(index0 < 0 || index0 > len0 - 1)
            {
               btn0.visible = false;
            }
            else
            {
               day0 = index0 + 1;
               btn0.visible = true;
               btn0.setName(String(day0));
               if(day0 == nowDate0.date)
               {
                  btn0.setNew(true);
               }
               date2 = nowDate0.copy();
               date2.date = day0;
               date_str2 = date2.getDateStr();
               if(dateArr0.indexOf(date_str2) >= 0)
               {
                  btn0.setSmallIcon("sign");
                  if(day0 == nowDate0.date)
                  {
                     todaySignB = true;
                  }
               }
               else
               {
                  btn0.setSmallIcon("");
               }
            }
         }
         return todaySignB;
      }
   }
}

