package UI.house
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripMoveCtrl;
   import UI.bag.ItemsGripTipCtrl;
   import UI.bag.ItemsGripUnlockCtrl;
   import UI.bag.moreChoose.ItemsMoreCtrlBox;
   import UI.base.AppNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.house.HouseDataCtrl;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class HouseUI extends AppNormalUI
   {
      
      public var labelBox:LabelBox = new LabelBox();
      
      public var armsBox:ItemsGripBox = new ItemsGripBox();
      
      public var equipBox:ItemsGripBox = new ItemsGripBox();
      
      private var nowBox:ItemsGripBox = null;
      
      public var boxArr:Array = [this.armsBox,this.equipBox];
      
      public var sortBtn:NormalBtn = new NormalBtn();
      
      public var dayBtn:NormalBtn = new NormalBtn();
      
      public var tipBtn:SimpleButton;
      
      public var sortBtnSp:MovieClip = null;
      
      public var dayBtnSp:MovieClip = null;
      
      public var labelTag:Sprite = null;
      
      public var gripTag:Sprite = null;
      
      public var equipPageTag:Sprite = null;
      
      public var moreBtnSp:MovieClip = null;
      
      public var moreBtn:NormalBtn = new NormalBtn();
      
      private var itemsMore:ItemsMoreCtrlBox = new ItemsMoreCtrlBox();
      
      public function HouseUI()
      {
         super();
         UICn = "仓库";
         this.armsBox.label = "arms";
         this.equipBox.label = "equip";
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["moreBtnSp","sortBtnSp","dayBtnSp","labelTag","gripTag","equipPageTag","tipBtn"];
         super.setImg(img0);
         addChildAt(img,0);
         addChild(this.armsBox);
         NormalUICtrl.setTag(this.armsBox,this.gripTag);
         this.armsBox.imgType = "HouseUI/armsGrip";
         this.armsBox.arg.init(3,6,3,3);
         this.armsBox.evt.setWantEvent(true,true,true,true,true,true);
         this.armsBox.pageBox.setToNormalBtn();
         this.armsBox.pageBox.setXY_bySp(this.equipPageTag,this.armsBox);
         addChild(this.equipBox);
         NormalUICtrl.setTag(this.equipBox,this.gripTag);
         this.equipBox.imgType = "equipGrip";
         this.equipBox.arg.init(8,6,1,1);
         this.equipBox.evt.setWantEvent(true,true,true,true,true,true);
         this.equipBox.pageBox.setToNormalBtn();
         this.equipBox.pageBox.setXY_bySp(this.equipPageTag,this.equipBox);
         addChild(this.labelBox);
         NormalUICtrl.setTag(this.labelBox,this.labelTag);
         this.labelBox.arg.init(5,1,-6,0);
         this.labelBox.inData("bagLabelBtn",["arms","equip"],["武器","装备"]);
         this.labelBox.setChoose("arms");
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         ItemsGripMoveCtrl.addEvent_byItemsGripBox(this.armsBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.armsBox);
         ItemsGripUnlockCtrl.addEvent_byItemsGripBox(this.armsBox);
         ItemsGripMoveCtrl.addEvent_byItemsGripBox(this.equipBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.equipBox);
         ItemsGripUnlockCtrl.addEvent_byItemsGripBox(this.equipBox);
         addChild(this.sortBtn);
         this.sortBtn.setImg(this.sortBtnSp);
         this.sortBtn.addEventListener(MouseEvent.CLICK,this.sortBagClick);
         addChild(this.dayBtn);
         this.dayBtn.setImg(this.dayBtnSp);
         this.dayBtn.addEventListener(MouseEvent.CLICK,this.dayClick);
         this.tipBtn.addEventListener(MouseEvent.CLICK,this.showGuide);
         this.moreBtn.setImg(this.moreBtnSp);
         this.moreBtn.addEventListener(MouseEvent.CLICK,this.moreBtnClick);
         this.itemsMore.setToNormalImg();
         addChild(this.itemsMore);
         addChild(this.moreBtn);
         this.itemsMore.x = this.moreBtn.x + this.moreBtn.width + 6 - this.itemsMore.width;
         this.itemsMore.y = this.moreBtn.y - 6;
         this.itemsMore.hide();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
         if(!Gaming.uiGroup.bagUI.visible)
         {
            Gaming.uiGroup.bagUI.showAndLabel(this.labelBox.nowLabel);
         }
         if(!Gaming.PG.save.guide.houseInfo)
         {
            this.showGuide();
         }
      }
      
      override public function hide() : void
      {
         super.hide();
         if(Gaming.uiGroup.bagUI.visible)
         {
            Gaming.uiGroup.bagUI.hide();
         }
      }
      
      public function fleshData() : void
      {
         if(this.visible)
         {
            this.showBox(this.labelBox.nowLabel);
         }
         this.fleshFill();
      }
      
      private function showGuide(e:MouseEvent = null) : void
      {
         Gaming.uiGroup.alertBox.info.showInfo("使用仓库须知","HouseUI/houseInfo","houseInfo");
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.hideItemsMore();
         this.showBox(e.label);
         Gaming.uiGroup.bagUI.showAndLabel(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         var n:* = undefined;
         if(!this.visible)
         {
            return;
         }
         Gaming.uiGroup.btnList.hide();
         this.labelBox.setChoose(label0);
         for(n in this.boxArr)
         {
            this.boxArr[n].visible = false;
         }
         this.sortBtn.visible = true;
         this.moreBtn.visible = false;
         if(label0 == "arms")
         {
            this.nowBox = this.armsBox;
            this.armsBox.visible = true;
            this.moreBtn.visible = true;
         }
         else if(label0 == "equip")
         {
            this.nowBox = this.equipBox;
            this.equipBox.visible = true;
            this.moreBtn.visible = true;
         }
         this.nowBox.inData_byDataGroup(Gaming.PG.da[label0 + "House"]);
         this.fleshGripDay();
         var dg0:ItemsDataGroup = this.nowBox.fatherData as ItemsDataGroup;
         this.sortBtn.setName("排序");
      }
      
      private function fleshFill() : void
      {
         var n:* = undefined;
         var btn0:NormalBtn = null;
         var space0:int = 0;
         var arr0:Array = this.labelBox.gripArr;
         for(n in arr0)
         {
            btn0 = arr0[n];
            space0 = Gaming.PG.da.getHouseSpaceByType(btn0.label);
            btn0.setSmallIcon(space0 == 0 ? "fill" : "");
         }
      }
      
      private function dayClick(e:MouseEvent) : void
      {
         var bb0:Boolean = !this.dayBtn.isChosen;
         this.dayBtn.isChosen = bb0;
         if(bb0)
         {
            this.showGripDay();
         }
         else
         {
            this.clearGripDay();
         }
      }
      
      private function fleshGripDay() : void
      {
         if(this.dayBtn.isChosen)
         {
            this.showGripDay();
         }
      }
      
      private function showGripDay() : void
      {
         var grip0:ItemsGrid = null;
         var da0:IO_ItemsData = null;
         var cDay0:int = 0;
         if(Boolean(this.nowBox))
         {
            for each(grip0 in this.nowBox.gripArr)
            {
               da0 = grip0.itemsData as IO_ItemsData;
               if(da0 is IO_ItemsData)
               {
                  cDay0 = HouseDataCtrl.countDay(da0,Gaming.PG.da.time.getReadTimeDate());
                  if(cDay0 > 0)
                  {
                     grip0.setNumText(ComMethod.color(cDay0 + "天","#999999"));
                  }
               }
            }
         }
      }
      
      private function clearGripDay() : void
      {
         var grip0:ItemsGrid = null;
         if(Boolean(this.nowBox))
         {
            for each(grip0 in this.nowBox.gripArr)
            {
               grip0.setNumText("");
            }
         }
      }
      
      private function sortBagClick(e:MouseEvent) : void
      {
         var wearDg0:ItemsDataGroup = null;
         if(Boolean(this.nowBox))
         {
            wearDg0 = Gaming.PG.da[this.nowBox.label + "House"];
            if(this.nowBox == this.equipBox)
            {
               this.nowBox.fatherData.sort2(wearDg0);
            }
            else
            {
               this.nowBox.fatherData.sort(wearDg0);
            }
            this.fleshData();
         }
      }
      
      private function moreBtnClick(e:MouseEvent) : void
      {
         if(this.itemsMore.visible)
         {
            this.hideItemsMore();
         }
         else
         {
            this.showItemsMore();
         }
      }
      
      private function showItemsMore() : void
      {
         this.moreBtn.setName("关闭");
         this.itemsMore.showBox(this.nowBox);
      }
      
      private function hideItemsMore() : void
      {
         this.moreBtn.setName("多选操作");
         this.itemsMore.hide();
      }
   }
}

