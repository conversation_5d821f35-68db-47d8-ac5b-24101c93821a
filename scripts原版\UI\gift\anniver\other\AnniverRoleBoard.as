package UI.gift.anniver.other
{
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class AnniverRoleBoard extends AutoNormalUI
   {
      
      private var gotoBtn:NormalBtn;
      
      public function AnniverRoleBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.gotoBtn.setName("前往任务");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.taskUI.gotoTaskName("shotgunBladeTrain");
      }
   }
}

