package UI.helper
{
   import UI.base.AutoNormalUI;
   import com.sounto.utils.TextMethod;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class Hep<PERSON><PERSON><PERSON>inkText extends AutoNormalUI
   {
      
      protected var linkTxt:TextField;
      
      public function HeplperLinkText()
      {
         super();
         mcTypeArr = ["txt"];
      }
      
      public function tryNormalImg() : void
      {
         if(!img)
         {
            this.setImg(Gaming.swfLoaderManager.getResourceFull("HelperUI/linkTextSp"));
         }
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.linkTxt.styleSheet = TextMethod.getLinkCss();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
   }
}

