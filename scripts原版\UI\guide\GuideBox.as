package UI.guide
{
   import UI.base._hide.HideNormalUI;
   import com.greensock.TweenLite;
   import fl.transitions.easing.Regular;
   import flash.display.DisplayObject;
   import flash.display.Graphics;
   import flash.display.Shape;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import flash.text.TextField;
   
   public class Guide<PERSON>ox extends HideNormalUI
   {
      
      private var box:Sprite;
      
      private var boxTxt:TextField;
      
      private var pointer:Sprite;
      
      private var backSh:Shape = new Shape();
      
      public function GuideBox()
      {
         super();
      }
      
      override protected function firstLoad() : void
      {
         this.setImg(Gaming.swfLoaderManager.getResource("GuideBox","guideBox"));
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["box","pointer"];
         super.setImg(img0);
         this.boxTxt = this.box["txt"];
         this.box["closeBtn"].addEventListener(MouseEvent.CLICK,this.closeClick);
         addChildAt(this.backSh,0);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function showText(sp0:DisplayObject, str0:String, sp2:DisplayObject = null) : void
      {
         show();
         this.boxTxt.text = str0;
         this.setPosition(sp0);
         this.setBackBySp(sp0,sp2);
      }
      
      private function setPosition(sp0:DisplayObject) : void
      {
         var rect0:Rectangle = null;
         rect0 = sp0.getRect(Gaming.gameSprite);
         var x0:Number = 0;
         var y0:Number = 0;
         if(rect0.y > Gaming.HEIGHT - 100)
         {
            this.pointer.rotation = 90;
            this.pointer.x = rect0.x + rect0.width / 2;
            this.pointer.y = rect0.y;
            x0 = this.pointer.x - this.box.width / 2;
            y0 = this.pointer.y - this.box.height - 40;
         }
         else if(rect0.y < 100)
         {
            this.pointer.rotation = -90;
            this.pointer.x = rect0.x + rect0.width / 2;
            this.pointer.y = rect0.y + rect0.height;
            x0 = this.pointer.x - this.box.width / 2;
            y0 = this.pointer.y + 40;
         }
         else if(rect0.x + rect0.width > Gaming.WIDTH / 2)
         {
            this.pointer.rotation = 0;
            this.pointer.x = rect0.x;
            this.pointer.y = rect0.y + rect0.height / 2;
            x0 = this.pointer.x - this.box.width - 40;
            y0 = this.pointer.y - this.box.height / 2;
         }
         else
         {
            this.pointer.rotation = 180;
            this.pointer.x = rect0.x + rect0.width;
            this.pointer.y = rect0.y + rect0.height / 2;
            x0 = this.pointer.x + 40;
            y0 = this.pointer.y - this.box.height / 2;
         }
         this.pointer.visible = false;
         TweenLite.to(this.box,0.5,{
            "x":x0,
            "y":y0,
            "ease":Regular.easeOut,
            "onComplete":this.showPointer
         });
      }
      
      private function showPointer() : void
      {
         this.pointer.visible = true;
         this.pointer.alpha = 0;
         TweenLite.to(this.pointer,0.3,{"alpha":1});
      }
      
      private function setBackBySp(sp0:DisplayObject, sp2:DisplayObject = null) : void
      {
         var rect0:Rectangle = sp0.getRect(Gaming.gameSprite);
         var gh0:Graphics = this.backSh.graphics;
         gh0.clear();
         gh0.beginFill(0,0.65);
         gh0.drawRect(0,0,Gaming.WIDTH,Gaming.HEIGHT);
         this.drawRect(sp0);
         if(Boolean(sp2))
         {
            this.drawRect(sp2);
         }
      }
      
      private function drawRect(sp0:DisplayObject) : void
      {
         var rect0:Rectangle = sp0.getRect(Gaming.gameSprite);
         var gh0:Graphics = this.backSh.graphics;
         gh0.lineStyle(4,16737792,0.8,false,"normal","none","miter");
         gh0.moveTo(rect0.x,rect0.y);
         gh0.lineTo(rect0.x + rect0.width,rect0.y);
         gh0.lineTo(rect0.x + rect0.width,rect0.y + rect0.height);
         gh0.lineTo(rect0.x,rect0.y + rect0.height);
         gh0.lineTo(rect0.x,rect0.y);
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         GuideOrder.overGuide();
         Gaming.soundGroup.playSound("uiSound","click");
      }
   }
}

