package UI.gift.anniver.other
{
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.pay.PayCtrl;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class AnniverPayBoard extends AutoNormalUI
   {
      
      private var gotoBtn:NormalBtn;
      
      public function AnniverPayBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.gotoBtn.setName("充值");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         PayCtrl.gotoPay();
      }
   }
}

